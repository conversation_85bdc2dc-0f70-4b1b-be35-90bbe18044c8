package handlers.effecthandlers;

import java.util.EnumSet;

import org.l2jmobius.gameserver.model.StatSet;
import org.l2jmobius.gameserver.model.actor.Creature;
import org.l2jmobius.gameserver.model.actor.Player;
import org.l2jmobius.gameserver.model.costumes.Costume;
import org.l2jmobius.gameserver.model.costumes.CostumeGrade;
import org.l2jmobius.gameserver.model.effects.AbstractEffect;
import org.l2jmobius.gameserver.model.item.instance.Item;
import org.l2jmobius.gameserver.model.skill.Skill;
import org.l2jmobius.gameserver.network.serverpackets.costume.ExCostumeUseItem;
import org.l2jmobius.gameserver.network.serverpackets.costume.ExSendCostumeList;

public class AcquireRandomCostume extends AbstractEffect
{
	private final EnumSet<CostumeGrade> grades;
	
	public AcquireRandomCostume(StatSet params)
	{
		grades = params.getStringAsEnumSet("grades", CostumeGrade.class, ",");
	}
	
	@Override
	public boolean isInstant()
	{
		return true;
	}
	
	@Override
	public void instant(Creature effector, Creature effected, Skill skill, Item item)
	{
		final Player player = effected.getActingPlayer();
		if (player == null)
		{
			return;
		}
		CostumeRecord randomCostume = CostumeManager.getInstance().getRandomCostume(grades);
		Costume costume = player.addCostume(randomCostume.id());
		player.sendPacket(new ExCostumeUseItem(costume.getId(), true));
		player.sendPacket(new ExSendCostumeList(costume));
		player.destroyItem("Consume", item, 1, null, true);
	}
}
